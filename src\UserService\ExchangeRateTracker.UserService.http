@Host = http://localhost:8081

# Register a new user
POST {{Host}}/api/auth/register
Content-Type: application/json

{
  "name": "<PERSON>",
  "password": "SecurePassword123!"
}

###

# Login with user credentials
POST {{Host}}/api/auth/login
Content-Type: application/json

{
  "name": "John Doe",
  "password": "SecurePassword123!"
}

###

# Get current user profile (requires authentication)
GET {{Host}}/api/auth/me
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Accept: application/json

###

# Logout (requires authentication)
POST {{Host}}/api/auth/logout
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

###
