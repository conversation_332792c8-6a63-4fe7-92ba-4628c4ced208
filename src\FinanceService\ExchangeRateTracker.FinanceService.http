@Host = http://localhost:8082

# Get all currencies (requires authentication)
GET {{Host}}/api/currencies
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Accept: application/json

###

# Get user's favorite currencies (requires authentication)
GET {{Host}}/api/currencies/favorites
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Accept: application/json

###

# Add currency to favorites (requires authentication)
POST {{Host}}/api/currencies/favorites
Authorization: Bearer YOUR_JWT_TOKEN_HERE
Content-Type: application/json

{
  "currencyId": 1
}

###

# Remove currency from favorites (requires authentication)
DELETE {{Host}}/api/currencies/favorites/1
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###
